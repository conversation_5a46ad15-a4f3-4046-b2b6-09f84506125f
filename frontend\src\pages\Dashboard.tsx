import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { useTimezone } from '../contexts/TimezoneContext';
import eventBus, { EVENT_TYPES } from '../utils/eventBus';
import {
  appointmentService,
  AppointmentStatus,
  userService,
  serviceService,
  Activity,
  UserRole
} from '../services';
import { getRecentActivities } from '../utils/activityUtils';
import {
  FiCalendar,
  FiClock,
  FiUsers,
  FiDollarSign,
  FiArrowRight,
  FiPlus,
  FiCheckCircle,
  FiXCircle,
  FiPieChart,
  FiGrid,
  FiTrendingUp
} from 'react-icons/fi';

const Dashboard: React.FC = () => {
  const { user, tenant } = useAuth();
  const { formatPrice } = useCurrency();
  const { formatDate: formatDateWithTimezone, formatTime: formatTimeWithTimezone } = useTimezone();


  const [upcomingAppointments, setUpcomingAppointments] = useState<any[]>([]);
  const [todayAppointments, setTodayAppointments] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  // These states are used but TypeScript doesn't detect it
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [recentClients, setRecentClients] = useState<any[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [recentServices, setRecentServices] = useState<any[]>([]);
  const [stats, setStats] = useState({
    totalClients: 0,
    totalAppointments: 0,
    completedAppointments: 0,
    cancelledAppointments: 0,
    revenue: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Define fetchData as a useCallback to avoid recreating it on every render
  const fetchData = useCallback(async () => {
    if (!user || !tenant) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get today's date
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get tomorrow's date
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get end of week date
      const endOfWeek = new Date(today);
      endOfWeek.setDate(endOfWeek.getDate() + 7);

      // Format dates for API
      const todayStr = today.toISOString();
      const tomorrowStr = tomorrow.toISOString();
      const endOfWeekStr = endOfWeek.toISOString();

      let todayResponse = [];
      let upcomingResponse = [];
      let clientsResponse = [];
      let servicesResponse = [];
      let allAppointments = [];

      // Fetch today's appointments
      try {
        todayResponse = await appointmentService.getAppointments(tenant.id, {
          startDate: todayStr,
          endDate: tomorrowStr,
          status: AppointmentStatus.CONFIRMED,
        });
      } catch (err) {
        console.error('Dashboard: Error fetching today appointments:', err);
        todayResponse = [];
      }

      // Fetch upcoming appointments (next 7 days)
      try {
        upcomingResponse = await appointmentService.getAppointments(tenant.id, {
          startDate: tomorrowStr,
          endDate: endOfWeekStr,
          status: AppointmentStatus.CONFIRMED,
        });
      } catch (err) {
        console.error('Dashboard: Error fetching upcoming appointments:', err);
        upcomingResponse = [];
      }

      // Fetch clients
      try {
        clientsResponse = await userService.getUsers(tenant.id, UserRole.CLIENT);
      } catch (err) {
        console.error('Dashboard: Error fetching clients:', err);
        clientsResponse = [];
      }

      // Get recent clients (last 10)
      const sortedClients = Array.isArray(clientsResponse) ? [...clientsResponse].sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ).slice(0, 10) : [];
      setRecentClients(sortedClients);

      // Fetch services
      try {
        servicesResponse = await serviceService.getServices(tenant.id);
      } catch (err) {
        console.error('Dashboard: Error fetching services:', err);
        servicesResponse = [];
      }

      // Get recent services (last 10)
      const sortedServices = Array.isArray(servicesResponse) ? [...servicesResponse].sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ).slice(0, 10) : [];
      setRecentServices(sortedServices);

      // Calculate stats
      try {
        allAppointments = await appointmentService.getAppointments(tenant.id, {});
      } catch (err) {
        console.error('Dashboard: Error fetching all appointments:', err);
        allAppointments = [];
      }

      const appointmentsArray = Array.isArray(allAppointments) ? allAppointments : [];
      const completed = appointmentsArray.filter((app: any) => app.status === AppointmentStatus.COMPLETED).length;
      const cancelled = appointmentsArray.filter((app: any) => app.status === AppointmentStatus.CANCELLED).length;

      // Calculate revenue (simplified)
      const revenue = appointmentsArray
        .filter((app: any) => app.status === AppointmentStatus.COMPLETED)
        .reduce((sum: number, app: any) => sum + (app.service?.price || 0), 0);

      // Get recent appointments (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentAppointments = appointmentsArray.filter(
        (app: any) => new Date(app.updatedAt || app.createdAt) >= thirtyDaysAgo
      );

      // Generate recent activities
      const activities = await getRecentActivities(
        recentAppointments,
        sortedClients,
        sortedServices,
        10
      );

      setRecentActivities(activities);
      setTodayAppointments(todayResponse);
      setUpcomingAppointments(upcomingResponse);
      setStats({
        totalClients: Array.isArray(clientsResponse) ? clientsResponse.length : 0,
        totalAppointments: appointmentsArray.length,
        completedAppointments: completed,
        cancelledAppointments: cancelled,
        revenue
      });
    } catch (err: any) {
      setError('Failed to load dashboard data. Please try again later.');
      console.error('Error fetching dashboard data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user, tenant]);

  // Initial data fetch when component mounts
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Listen for data change events
  useEffect(() => {
    // Handler for data change events
    const handleDataChange = () => {
      console.log('Dashboard: Data changed, refreshing...');
      fetchData();
    };

    // Subscribe to events
    eventBus.on(EVENT_TYPES.DATA_CHANGED, handleDataChange);
    eventBus.on(EVENT_TYPES.APPOINTMENT_CREATED, handleDataChange);
    eventBus.on(EVENT_TYPES.APPOINTMENT_UPDATED, handleDataChange);
    eventBus.on(EVENT_TYPES.APPOINTMENT_DELETED, handleDataChange);
    eventBus.on(EVENT_TYPES.CLIENT_CREATED, handleDataChange);
    eventBus.on(EVENT_TYPES.CLIENT_DELETED, handleDataChange);

    // Cleanup function to unsubscribe from events
    return () => {
      eventBus.off(EVENT_TYPES.DATA_CHANGED, handleDataChange);
      eventBus.off(EVENT_TYPES.APPOINTMENT_CREATED, handleDataChange);
      eventBus.off(EVENT_TYPES.APPOINTMENT_UPDATED, handleDataChange);
      eventBus.off(EVENT_TYPES.APPOINTMENT_DELETED, handleDataChange);
      eventBus.off(EVENT_TYPES.CLIENT_CREATED, handleDataChange);
      eventBus.off(EVENT_TYPES.CLIENT_DELETED, handleDataChange);
    };
  }, [fetchData]);

  const formatAppointmentTime = (dateString: string) => {
    const date = new Date(dateString);
    // Use the timezone context to format the time
    return formatTimeWithTimezone(date);
  };

  const formatAppointmentDate = (dateString: string) => {
    const date = new Date(dateString);
    // Use the timezone context to format the date
    return formatDateWithTimezone(date).split(',')[0]; // Get only the date part
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case AppointmentStatus.CONFIRMED:
        return 'bg-blue-100 text-blue-800';
      case AppointmentStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case AppointmentStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  return (
    <Layout>
      <div className="max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 rounded-2xl shadow-xl mb-10 p-8 text-white">
          <div className="absolute inset-0 opacity-10">
            <svg className="h-full w-full" viewBox="0 0 800 800">
              <path d="M435.5,160.5Q432,321,271,329Q110,337,97.5,168.5Q85,0,272.5,0Q460,0,447.5,80Q435,160,435.5,160.5Z" fill="white"></path>
              <path d="M215.5,279.5Q142,559,71,279.5Q0,0,357.5,0Q715,0,465,279.5Q215,559,215.5,279.5Z" fill="white"></path>
            </svg>
          </div>

          <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <span className="inline-block px-3 py-1 bg-white/20 rounded-full text-sm font-medium mb-3">
                {new Date().toLocaleDateString([], { weekday: 'long', month: 'long', day: 'numeric' })}
              </span>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">Welcome back, {user?.firstName}!</h1>
              <p className="text-blue-100">Here's what's happening with your business today</p>
            </div>
            <div className="mt-6 md:mt-0 flex flex-wrap gap-3">
              <Link
                to="/appointments/new"
                className="flex items-center bg-white text-blue-700 px-5 py-2.5 rounded-lg font-medium hover:bg-blue-50 transition-all shadow-md hover:-translate-y-1"
              >
                <FiPlus className="mr-2" />
                New Appointment
              </Link>
              <Link
                to="/clients/new"
                className="flex items-center bg-blue-500/30 text-white border border-white/20 px-5 py-2.5 rounded-lg font-medium hover:bg-blue-500/50 transition-all"
              >
                <FiUsers className="mr-2" />
                Add Client
              </Link>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-8 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg shadow-md">
            <div className="flex items-center">
              <svg className="h-6 w-6 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <p className="font-medium">{error}</p>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600 mb-4"></div>
            <p className="text-gray-500 font-medium">Loading dashboard data...</p>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
              <div className="bg-white rounded-2xl shadow-md p-6 border border-gray-100 transition-all hover:shadow-lg hover:-translate-y-1 duration-300">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-blue-100 text-blue-600 mr-4">
                    <FiCalendar className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Total Appointments</p>
                    <p className="text-2xl font-bold text-gray-800">{stats.totalAppointments}</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center text-sm text-blue-600">
                    <FiTrendingUp className="mr-1" />
                    <span className="font-medium">12% increase</span>
                    <span className="text-gray-500 ml-1">from last month</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl shadow-md p-6 border border-gray-100 transition-all hover:shadow-lg hover:-translate-y-1 duration-300">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-green-100 text-green-600 mr-4">
                    <FiCheckCircle className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Completed</p>
                    <p className="text-2xl font-bold text-gray-800">{stats.completedAppointments}</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center text-sm text-green-600">
                    <FiTrendingUp className="mr-1" />
                    <span className="font-medium">8% increase</span>
                    <span className="text-gray-500 ml-1">from last month</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl shadow-md p-6 border border-gray-100 transition-all hover:shadow-lg hover:-translate-y-1 duration-300">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-red-100 text-red-600 mr-4">
                    <FiXCircle className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Cancelled</p>
                    <p className="text-2xl font-bold text-gray-800">{stats.cancelledAppointments}</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center text-sm text-red-600">
                    <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                    <span className="font-medium">3% decrease</span>
                    <span className="text-gray-500 ml-1">from last month</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl shadow-md p-6 border border-gray-100 transition-all hover:shadow-lg hover:-translate-y-1 duration-300">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-purple-100 text-purple-600 mr-4">
                    <FiUsers className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Total Clients</p>
                    <p className="text-2xl font-bold text-gray-800">{stats.totalClients}</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center text-sm text-purple-600">
                    <FiTrendingUp className="mr-1" />
                    <span className="font-medium">5% increase</span>
                    <span className="text-gray-500 ml-1">from last month</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
              {/* Today's Appointments */}
              <div className="bg-white rounded-2xl shadow-md overflow-hidden lg:col-span-2 border border-gray-100">
                <div className="px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <FiCalendar className="h-5 w-5 text-blue-600 mr-2" />
                      <h2 className="text-xl font-bold text-gray-800">Today's Appointments</h2>
                    </div>
                    <Link to="/appointments" className="text-blue-600 hover:text-blue-800 flex items-center text-sm font-medium bg-white px-3 py-1 rounded-lg shadow-sm">
                      View All <FiArrowRight className="ml-1" />
                    </Link>
                  </div>
                </div>
                <div className="p-6">
                  {todayAppointments.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <div className="p-4 rounded-full bg-blue-50 mb-4">
                        <FiCalendar className="h-10 w-10 text-blue-500" />
                      </div>
                      <p className="text-gray-500 mb-3">No appointments scheduled for today</p>
                      <Link
                        to="/appointments/new"
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors shadow-md flex items-center"
                      >
                        <FiPlus className="mr-2" />
                        Create New Appointment
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {todayAppointments.map((appointment) => (
                        <div
                          key={appointment.id}
                          className="flex items-center p-4 rounded-xl border border-gray-100 hover:bg-blue-50/30 transition-all hover:shadow-md"
                        >
                          <div className="flex-shrink-0 mr-4">
                            <div className="w-14 h-14 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-lg shadow-sm">
                              {appointment.client.firstName.charAt(0)}{appointment.client.lastName.charAt(0)}
                            </div>
                          </div>
                          <div className="flex-grow">
                            <div className="flex justify-between">
                              <p className="font-medium text-gray-800 text-lg">
                                {appointment.client.firstName} {appointment.client.lastName}
                              </p>
                              <span className={`text-xs px-3 py-1 rounded-full font-medium ${getStatusColor(appointment.status)}`}>
                                {appointment.status}
                              </span>
                            </div>
                            <p className="text-gray-600">{appointment.service.name}</p>
                            <div className="flex items-center text-gray-500 text-sm mt-1">
                              <FiClock className="mr-1 text-blue-500" />
                              {formatAppointmentTime(appointment.startTime)} - {formatAppointmentTime(appointment.endTime)}
                            </div>
                          </div>
                          <Link
                            to={`/appointments/${appointment.id}`}
                            className="ml-4 p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-all"
                          >
                            <FiArrowRight />
                          </Link>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-6">
                <div className="bg-white rounded-2xl shadow-md overflow-hidden border border-gray-100">
                  <div className="px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
                    <div className="flex items-center">
                      <FiGrid className="h-5 w-5 text-indigo-600 mr-2" />
                      <h2 className="text-xl font-bold text-gray-800">Quick Actions</h2>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-1 gap-3">
                      <Link
                        to="/appointments/new"
                        className="flex items-center p-3 rounded-xl border border-gray-100 hover:bg-blue-50 hover:border-blue-200 transition-all hover:shadow-md"
                      >
                        <div className="p-3 rounded-lg bg-blue-100 text-blue-600 mr-3">
                          <FiCalendar className="h-5 w-5" />
                        </div>
                        <span className="font-medium text-gray-800">New Appointment</span>
                      </Link>

                      <Link
                        to="/clients/new"
                        className="flex items-center p-3 rounded-xl border border-gray-100 hover:bg-green-50 hover:border-green-200 transition-all hover:shadow-md"
                      >
                        <div className="p-3 rounded-lg bg-green-100 text-green-600 mr-3">
                          <FiUsers className="h-5 w-5" />
                        </div>
                        <span className="font-medium text-gray-800">Add New Client</span>
                      </Link>

                      <Link
                        to="/services"
                        className="flex items-center p-3 rounded-xl border border-gray-100 hover:bg-purple-50 hover:border-purple-200 transition-all hover:shadow-md"
                      >
                        <div className="p-3 rounded-lg bg-purple-100 text-purple-600 mr-3">
                          <FiDollarSign className="h-5 w-5" />
                        </div>
                        <span className="font-medium text-gray-800">Manage Services</span>
                      </Link>

                      <Link
                        to="/analytics"
                        className="flex items-center p-3 rounded-xl border border-gray-100 hover:bg-indigo-50 hover:border-indigo-200 transition-all hover:shadow-md"
                      >
                        <div className="p-3 rounded-lg bg-indigo-100 text-indigo-600 mr-3">
                          <FiPieChart className="h-5 w-5" />
                        </div>
                        <span className="font-medium text-gray-800">View Analytics</span>
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Revenue Card */}
                <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl shadow-md overflow-hidden text-white p-6">
                  <div className="flex items-center mb-4">
                    <div className="p-3 rounded-lg bg-white/20 mr-3">
                      <FiDollarSign className="h-6 w-6" />
                    </div>
                    <h3 className="text-xl font-bold">Revenue</h3>
                  </div>
                  <p className="text-3xl font-bold mb-2">{formatPrice(stats.revenue)}</p>
                  <div className="flex items-center text-sm text-white/80">
                    <FiTrendingUp className="mr-1" />
                    <span>15% increase from last month</span>
                  </div>
                  <Link to="/analytics" className="mt-4 inline-flex items-center text-sm font-medium bg-white/20 hover:bg-white/30 px-3 py-1.5 rounded-lg transition-colors">
                    View Details <FiArrowRight className="ml-1" />
                  </Link>
                </div>
              </div>
            </div>

            {/* Upcoming Appointments */}
            <div className="bg-white rounded-2xl shadow-md overflow-hidden mb-10 border border-gray-100">
              <div className="px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <FiClock className="h-5 w-5 text-purple-600 mr-2" />
                    <h2 className="text-xl font-bold text-gray-800">Upcoming Appointments</h2>
                  </div>
                  <Link to="/appointments" className="text-blue-600 hover:text-blue-800 flex items-center text-sm font-medium bg-white px-3 py-1 rounded-lg shadow-sm">
                    View All <FiArrowRight className="ml-1" />
                  </Link>
                </div>
              </div>
              <div className="p-6">
                {upcomingAppointments.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <div className="p-4 rounded-full bg-purple-50 mb-4">
                      <FiCalendar className="h-10 w-10 text-purple-500" />
                    </div>
                    <p className="text-gray-500 mb-3">No upcoming appointments scheduled</p>
                    <Link
                      to="/appointments/new"
                      className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors shadow-md flex items-center"
                    >
                      <FiPlus className="mr-2" />
                      Create New Appointment
                    </Link>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Client
                          </th>
                          <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Service
                          </th>
                          <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date & Time
                          </th>
                          <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="relative px-6 py-4">
                            <span className="sr-only">Actions</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {upcomingAppointments.map((appointment, index) => (
                          <tr key={appointment.id} className={`hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'}`}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold shadow-sm">
                                  {appointment.client.firstName.charAt(0)}{appointment.client.lastName.charAt(0)}
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {appointment.client.firstName} {appointment.client.lastName}
                                  </div>
                                  <div className="text-sm text-gray-500">{appointment.client.email}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{appointment.service.name}</div>
                              <div className="text-sm text-gray-500">{formatPrice(appointment.service.price)}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{formatAppointmentDate(appointment.startTime)}</div>
                              <div className="text-sm text-gray-500 flex items-center">
                                <FiClock className="mr-1 text-blue-500 h-3 w-3" />
                                {formatAppointmentTime(appointment.startTime)} - {formatAppointmentTime(appointment.endTime)}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-3 py-1 inline-flex text-xs leading-5 font-medium rounded-full ${getStatusColor(appointment.status)}`}>
                                {appointment.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <Link
                                to={`/appointments/${appointment.id}`}
                                className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-lg transition-colors"
                              >
                                View
                              </Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-2xl shadow-md overflow-hidden mb-10 border border-gray-100">
              <div className="px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-cyan-50">
                <div className="flex items-center">
                  <FiRefreshCw className="h-5 w-5 text-blue-600 mr-2" />
                  <h2 className="text-xl font-bold text-gray-800">Recent Activity</h2>
                </div>
              </div>
              <div className="p-6">
                <div className="relative">
                  {/* Timeline line */}
                  <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                  {recentActivities.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <div className="p-4 rounded-full bg-blue-50 mb-4">
                        <FiRefreshCw className="h-10 w-10 text-blue-500" />
                      </div>
                      <p className="text-gray-500 mb-3">No recent activity to display</p>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {recentActivities.map((activity) => {
                        // Determine icon based on activity type
                        let Icon = FiCalendar;
                        let bgColor = 'bg-blue-500';

                        if (activity.icon === 'check-circle') {
                          Icon = FiCheckCircle;
                          bgColor = 'bg-green-500';
                        } else if (activity.icon === 'x-circle') {
                          Icon = FiXCircle;
                          bgColor = 'bg-red-500';
                        } else if (activity.icon === 'users') {
                          Icon = FiUsers;
                          bgColor = 'bg-purple-500';
                        } else if (activity.icon === 'dollar-sign') {
                          Icon = FiDollarSign;
                          bgColor = 'bg-green-500';
                        } else if (activity.icon === 'refresh-cw') {
                          Icon = FiRefreshCw;
                          bgColor = 'bg-blue-500';
                        }

                        return (
                          <div key={activity.id} className="flex">
                            <div className="flex-shrink-0 z-10">
                              <div className={`h-8 w-8 rounded-full ${bgColor} flex items-center justify-center text-white shadow-md`}>
                                <Icon className="h-4 w-4" />
                              </div>
                            </div>
                            <div className="ml-6">
                              <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                              <p className="text-sm text-gray-500">{activity.description}</p>
                              <p className="text-xs text-gray-400 mt-1">{formatRelativeTime(activity.timestamp)}</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </Layout>
  );
};

export default Dashboard;
