import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { useTimezone } from '../contexts/TimezoneContext';
import eventBus, { EVENT_TYPES } from '../utils/eventBus';
import {
  appointmentService,
  AppointmentStatus,
  userService,
  serviceService,
  Activity,
  UserRole
} from '../services';
import { getRecentActivities } from '../utils/activityUtils';
import {
  FiCalendar,
  FiClock,
  FiUsers,
  FiDollarSign,
  FiArrowRight,
  FiPlus,
  FiCheckCircle,
  FiXCircle,
  FiPieChart,
  FiGrid,
  FiTrendingUp,
  FiRefreshCw
} from 'react-icons/fi';

const Dashboard: React.FC = () => {
  const { user, tenant } = useAuth();
  const { formatPrice } = useCurrency();
  const { formatDate: formatDateWithTimezone, formatTime: formatTimeWithTimezone } = useTimezone();

  console.log('🔍 Dashboard Component Render:', {
    user: user ? { id: user.id, role: user.role, tenantId: user.tenantId } : null,
    tenant: tenant ? { id: tenant.id, name: tenant.name } : null,
    timestamp: new Date().toISOString()
  });


  const [upcomingAppointments, setUpcomingAppointments] = useState<any[]>([]);
  const [todayAppointments, setTodayAppointments] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  // These states are used but TypeScript doesn't detect it
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [recentClients, setRecentClients] = useState<any[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [recentServices, setRecentServices] = useState<any[]>([]);
  const [stats, setStats] = useState({
    totalClients: 0,
    totalAppointments: 0,
    completedAppointments: 0,
    cancelledAppointments: 0,
    revenue: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Define fetchData as a useCallback to avoid recreating it on every render
  const fetchData = useCallback(async () => {
    if (!user || !tenant) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get today's date
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get tomorrow's date
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get end of week date
      const endOfWeek = new Date(today);
      endOfWeek.setDate(endOfWeek.getDate() + 7);

      // Format dates for API
      const todayStr = today.toISOString();
      const tomorrowStr = tomorrow.toISOString();
      const endOfWeekStr = endOfWeek.toISOString();

      let todayResponse = [];
      let upcomingResponse = [];
      let clientsResponse = [];
      let servicesResponse = [];
      let allAppointments = [];

      // Fetch today's appointments
      try {
        todayResponse = await appointmentService.getAppointments(tenant.id, {
          startDate: todayStr,
          endDate: tomorrowStr,
          status: AppointmentStatus.CONFIRMED,
        });
      } catch (err) {
        console.error('Dashboard: Error fetching today appointments:', err);
        todayResponse = [];
      }

      // Fetch upcoming appointments (next 7 days)
      try {
        upcomingResponse = await appointmentService.getAppointments(tenant.id, {
          startDate: tomorrowStr,
          endDate: endOfWeekStr,
          status: AppointmentStatus.CONFIRMED,
        });
      } catch (err) {
        console.error('Dashboard: Error fetching upcoming appointments:', err);
        upcomingResponse = [];
      }

      // Fetch clients
      try {
        clientsResponse = await userService.getUsers(tenant.id, UserRole.CLIENT);
      } catch (err) {
        console.error('Dashboard: Error fetching clients:', err);
        clientsResponse = [];
      }

      // Get recent clients (last 10)
      const sortedClients = Array.isArray(clientsResponse) ? [...clientsResponse].sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ).slice(0, 10) : [];
      setRecentClients(sortedClients);

      // Fetch services
      try {
        servicesResponse = await serviceService.getServices(tenant.id);
      } catch (err) {
        console.error('Dashboard: Error fetching services:', err);
        servicesResponse = [];
      }

      // Get recent services (last 10)
      const sortedServices = Array.isArray(servicesResponse) ? [...servicesResponse].sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ).slice(0, 10) : [];
      setRecentServices(sortedServices);

      // Calculate stats
      try {
        allAppointments = await appointmentService.getAppointments(tenant.id, {});
      } catch (err) {
        console.error('Dashboard: Error fetching all appointments:', err);
        allAppointments = [];
      }

      const appointmentsArray = Array.isArray(allAppointments) ? allAppointments : [];
      const completed = appointmentsArray.filter((app: any) => app.status === AppointmentStatus.COMPLETED).length;
      const cancelled = appointmentsArray.filter((app: any) => app.status === AppointmentStatus.CANCELLED).length;

      // Calculate revenue (simplified)
      const revenue = appointmentsArray
        .filter((app: any) => app.status === AppointmentStatus.COMPLETED)
        .reduce((sum: number, app: any) => sum + (app.service?.price || 0), 0);

      // Get recent appointments (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentAppointments = appointmentsArray.filter(
        (app: any) => new Date(app.updatedAt || app.createdAt) >= thirtyDaysAgo
      );

      // Generate recent activities
      const activities = await getRecentActivities(
        recentAppointments,
        sortedClients,
        sortedServices,
        10
      );

      setRecentActivities(activities);
      setTodayAppointments(todayResponse);
      setUpcomingAppointments(upcomingResponse);
      setStats({
        totalClients: Array.isArray(clientsResponse) ? clientsResponse.length : 0,
        totalAppointments: appointmentsArray.length,
        completedAppointments: completed,
        cancelledAppointments: cancelled,
        revenue
      });
    } catch (err: any) {
      setError('Failed to load dashboard data. Please try again later.');
      console.error('Error fetching dashboard data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user, tenant]);

  // Initial data fetch when component mounts
  useEffect(() => {
    if (user && tenant) {
      fetchData();
    }
  }, [user, tenant]); // Remove fetchData dependency to avoid infinite loop

  // Listen for data change events
  useEffect(() => {
    // Handler for data change events
    const handleDataChange = () => {
      console.log('Dashboard: Data changed, refreshing...');
      if (user && tenant) {
        fetchData();
      }
    };

    // Subscribe to events
    eventBus.on(EVENT_TYPES.DATA_CHANGED, handleDataChange);
    eventBus.on(EVENT_TYPES.APPOINTMENT_CREATED, handleDataChange);
    eventBus.on(EVENT_TYPES.APPOINTMENT_UPDATED, handleDataChange);
    eventBus.on(EVENT_TYPES.APPOINTMENT_DELETED, handleDataChange);
    eventBus.on(EVENT_TYPES.CLIENT_CREATED, handleDataChange);
    eventBus.on(EVENT_TYPES.CLIENT_DELETED, handleDataChange);

    // Cleanup function to unsubscribe from events
    return () => {
      eventBus.off(EVENT_TYPES.DATA_CHANGED, handleDataChange);
      eventBus.off(EVENT_TYPES.APPOINTMENT_CREATED, handleDataChange);
      eventBus.off(EVENT_TYPES.APPOINTMENT_UPDATED, handleDataChange);
      eventBus.off(EVENT_TYPES.APPOINTMENT_DELETED, handleDataChange);
      eventBus.off(EVENT_TYPES.CLIENT_CREATED, handleDataChange);
      eventBus.off(EVENT_TYPES.CLIENT_DELETED, handleDataChange);
    };
  }, [user, tenant]); // Remove fetchData dependency to avoid infinite loop

  const formatAppointmentTime = (dateString: string) => {
    const date = new Date(dateString);
    // Use the timezone context to format the time
    return formatTimeWithTimezone(date);
  };

  const formatAppointmentDate = (dateString: string) => {
    const date = new Date(dateString);
    // Use the timezone context to format the date
    return formatDateWithTimezone(date).split(',')[0]; // Get only the date part
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case AppointmentStatus.CONFIRMED:
        return 'bg-blue-100 text-blue-800';
      case AppointmentStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case AppointmentStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  // Early return for debugging - let's see if this simple version works
  if (!user || !tenant) {
    console.log('🔍 Dashboard: Missing user or tenant, showing loading...');
    return (
      <Layout>
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-16">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600 mb-4 mx-auto"></div>
              <p className="text-gray-500 font-medium">Loading user data...</p>
              <p className="text-sm text-gray-400 mt-2">User: {user ? 'Present' : 'Missing'}, Tenant: {tenant ? 'Present' : 'Missing'}</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto">
        {/* Simple Welcome Section for debugging */}
        <div className="bg-blue-600 rounded-lg p-8 text-white mb-8">
          <h1 className="text-3xl font-bold mb-2">🎉 Dashboard Working!</h1>
          <p className="text-blue-100">Welcome back, {user?.firstName}!</p>
          <p className="text-blue-200 text-sm mt-2">Tenant: {tenant?.name}</p>
          <p className="text-blue-200 text-sm">User ID: {user?.id}</p>
          <p className="text-blue-200 text-sm">Role: {user?.role}</p>
        </div>

        {/* Simple content for debugging */}
        <div className="bg-white rounded-lg p-6 shadow-md">
          <h2 className="text-2xl font-bold mb-4">🔧 Debug Information</h2>
          <div className="space-y-2 text-sm">
            <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
            <p><strong>Error:</strong> {error || 'None'}</p>
            <p><strong>Stats:</strong> {JSON.stringify(stats)}</p>
            <p><strong>Today Appointments:</strong> {todayAppointments.length}</p>
            <p><strong>Upcoming Appointments:</strong> {upcomingAppointments.length}</p>
            <p><strong>Recent Activities:</strong> {recentActivities.length}</p>
          </div>
        </div>

        {error && (
          <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}

        {isLoading && (
          <div className="mt-4 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
            <strong>Loading:</strong> Fetching dashboard data...
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Dashboard;
